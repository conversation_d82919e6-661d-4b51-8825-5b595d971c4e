# 🚀 دليل تحديث السيرفر - Server Update Guide

## 📋 ملخص التحديثات في هذه المحادثة

### 1️⃣ **إصلاح مشكلة عرض الصور في البروفايل**
- **المشكلة:** الصور لا تظهر في قسم Personal Info
- **السبب:** عدم تطابق مسارات URL
- **الحل:** توحيد استخدام دالة `get_file()` وإصلاح المسارات

---

## 📁 قائمة الملفات المحدثة

### 🔧 **الملفات الأساسية:**

#### 1. **ملف البروفايل الرئيسي**
```
resources/views/user/profile.blade.php
```
**التغيير:** `get_file('uploads/avatar/')` → `get_file('uploads/avatar')`

#### 2. **ملف الهيدر**
```
resources/views/partials/admin/header.blade.php
```
**التغيير:** إصلاح مسار الصورة في الهيدر العلوي

### 🔧 **الملفات الإضافية:**

#### 3. **ملفات العرض الأخرى**
```
resources/views/vender/profile.blade.php
resources/views/projects/users.blade.php
resources/views/vendor/Chatify/layouts/info.blade.php
resources/views/customer/index.blade.php
resources/views/clients/show.blade.php
resources/views/assets/index.blade.php
resources/views/clients/index.blade.php
resources/views/jobApplication/index.blade.php
resources/views/project_report/index.blade.php
resources/views/contract/show.blade.php
```

---

## 🚀 تعليمات النقل إلى السيرفر

### **الخطوة 1: تحضير الملفات**

#### أ. إنشاء مجلد للتحديث
```bash
mkdir server_update_$(date +%Y%m%d)
cd server_update_$(date +%Y%m%d)
```

#### ب. نسخ الملفات المحدثة
```bash
# نسخ الملفات الأساسية
cp /path/to/local/resources/views/user/profile.blade.php ./
cp /path/to/local/resources/views/partials/admin/header.blade.php ./

# نسخ الملفات الإضافية
cp /path/to/local/resources/views/vender/profile.blade.php ./
cp /path/to/local/resources/views/projects/users.blade.php ./
cp /path/to/local/resources/views/vendor/Chatify/layouts/info.blade.php ./
cp /path/to/local/resources/views/customer/index.blade.php ./
cp /path/to/local/resources/views/clients/show.blade.php ./
cp /path/to/local/resources/views/assets/index.blade.php ./
cp /path/to/local/resources/views/clients/index.blade.php ./
cp /path/to/local/resources/views/jobApplication/index.blade.php ./
cp /path/to/local/resources/views/project_report/index.blade.php ./
cp /path/to/local/resources/views/contract/show.blade.php ./
```

### **الخطوة 2: رفع الملفات للسيرفر**

#### أ. باستخدام SCP
```bash
scp -r server_update_$(date +%Y%m%d)/ <EMAIL>:/tmp/
```

#### ب. باستخدام FTP/SFTP
```bash
# استخدم برنامج FTP المفضل لديك
# أو استخدم command line
sftp <EMAIL>
put -r server_update_$(date +%Y%m%d)/
```

### **الخطوة 3: تطبيق التحديثات على السيرفر**

#### أ. الاتصال بالسيرفر
```bash
ssh <EMAIL>
```

#### ب. الانتقال لمجلد المشروع
```bash
cd /path/to/your/laravel/project
```

#### ج. إنشاء نسخة احتياطية
```bash
# إنشاء نسخة احتياطية من الملفات الحالية
mkdir backup_$(date +%Y%m%d_%H%M%S)
cp -r resources/views/user/profile.blade.php backup_$(date +%Y%m%d_%H%M%S)/
cp -r resources/views/partials/admin/header.blade.php backup_$(date +%Y%m%d_%H%M%S)/
# ... نسخ باقي الملفات
```

#### د. تطبيق التحديثات
```bash
# نسخ الملفات الجديدة
cp /tmp/server_update_*/profile.blade.php resources/views/user/
cp /tmp/server_update_*/header.blade.php resources/views/partials/admin/
# ... نسخ باقي الملفات
```

---

## 🧹 تعليمات تنظيف PHP

### **الخطوة 1: تنظيف Cache**
```bash
# تنظيف cache Laravel
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear

# تنظيف compiled views
php artisan view:cache
```

### **الخطوة 2: تنظيف OPcache (إذا كان مفعل)**
```bash
# إعادة تشغيل PHP-FPM
sudo systemctl restart php8.1-fpm  # أو الإصدار المناسب

# أو تنظيف OPcache برمجياً
php -r "if(function_exists('opcache_reset')) opcache_reset();"
```

### **الخطوة 3: تنظيف Session**
```bash
# تنظيف sessions (اختياري)
php artisan session:table  # إذا كنت تستخدم database sessions
# أو
rm -rf storage/framework/sessions/*  # إذا كنت تستخدم file sessions
```

### **الخطوة 4: إعادة تشغيل الخدمات**
```bash
# إعادة تشغيل Nginx
sudo systemctl restart nginx

# إعادة تشغيل Apache (إذا كنت تستخدمه)
sudo systemctl restart apache2

# إعادة تشغيل PHP-FPM
sudo systemctl restart php8.1-fpm
```

---

## ✅ التحقق من نجاح التحديث

### **1. فحص الملفات**
```bash
# التأكد من وجود الملفات
ls -la resources/views/user/profile.blade.php
ls -la resources/views/partials/admin/header.blade.php

# فحص محتوى الملف للتأكد من التحديث
grep "get_file('uploads/avatar')" resources/views/user/profile.blade.php
```

### **2. فحص الموقع**
- تسجيل الدخول للموقع
- الذهاب لصفحة البروفايل
- التأكد من ظهور الصورة في قسم Personal Info
- التأكد من ظهور الصورة في الهيدر العلوي

### **3. فحص الأخطاء**
```bash
# فحص logs Laravel
tail -f storage/logs/laravel.log

# فحص logs الخادم
tail -f /var/log/nginx/error.log  # Nginx
tail -f /var/log/apache2/error.log  # Apache
```

---

## 🚨 في حالة حدوث مشاكل

### **استرجاع النسخة الاحتياطية**
```bash
# استرجاع الملفات من النسخة الاحتياطية
cp backup_*/profile.blade.php resources/views/user/
cp backup_*/header.blade.php resources/views/partials/admin/
# ... استرجاع باقي الملفات

# تنظيف cache مرة أخرى
php artisan cache:clear
php artisan view:clear
```

### **فحص الأذونات**
```bash
# التأكد من أذونات الملفات
chmod 644 resources/views/user/profile.blade.php
chmod 644 resources/views/partials/admin/header.blade.php

# التأكد من ملكية الملفات
chown www-data:www-data resources/views/user/profile.blade.php
chown www-data:www-data resources/views/partials/admin/header.blade.php
```

---

## 📝 ملاحظات مهمة

1. **قم بعمل نسخة احتياطية دائماً** قبل تطبيق أي تحديثات
2. **اختبر التحديثات** على بيئة تطوير أولاً
3. **راقب logs الخادم** بعد التحديث
4. **تأكد من أذونات الملفات** بعد النسخ
5. **نظف cache PHP** بعد كل تحديث

---

## 🎯 النتيجة المتوقعة

بعد تطبيق هذه التحديثات:
- ✅ ستظهر الصور في قسم Personal Info
- ✅ ستظهر الصور في الهيدر العلوي
- ✅ ستكون المسارات متطابقة في جميع أجزاء النظام
- ✅ لن تظهر أخطاء 404 للصور المرفوعة
