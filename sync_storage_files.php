<?php
require_once 'vendor/autoload.php';

// Load <PERSON>vel app
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== Storage Files Synchronization ===\n\n";

function syncDirectory($storageDir, $publicDir) {
    $synced = 0;
    $errors = 0;
    
    if (!is_dir($storageDir)) {
        echo "Storage directory does not exist: $storageDir\n";
        return [$synced, $errors];
    }
    
    // Create public directory if it doesn't exist
    if (!file_exists($publicDir)) {
        mkdir($publicDir, 0755, true);
        echo "Created public directory: $publicDir\n";
    }
    
    // Get all files in storage directory
    $files = scandir($storageDir);
    
    foreach ($files as $file) {
        if ($file === '.' || $file === '..') continue;
        
        $storageFile = $storageDir . '/' . $file;
        $publicFile = $publicDir . '/' . $file;
        
        if (is_file($storageFile)) {
            // Copy file if it doesn't exist in public or if storage file is newer
            if (!file_exists($publicFile) || filemtime($storageFile) > filemtime($publicFile)) {
                if (copy($storageFile, $publicFile)) {
                    echo "Synced: $file\n";
                    $synced++;
                } else {
                    echo "Error syncing: $file\n";
                    $errors++;
                }
            }
        }
    }
    
    return [$synced, $errors];
}

// Sync avatar files
echo "Syncing avatar files...\n";
list($avatarSynced, $avatarErrors) = syncDirectory(
    storage_path('uploads/avatar'),
    public_path('storage/avatar')
);

// Sync logo files
echo "\nSyncing logo files...\n";
list($logoSynced, $logoErrors) = syncDirectory(
    storage_path('uploads/logo'),
    public_path('storage/logo')
);

// Sync other common directories
$directories = [
    'bill_attachments',
    'document',
    'documentUpload',
    'job',
    'journal_attachments',
    'landing_page_image',
    'login_customization',
    'meta',
    'payment',
    'pro_image',
    'purchase',
    'sample',
    'supports'
];

$totalSynced = $avatarSynced + $logoSynced;
$totalErrors = $avatarErrors + $logoErrors;

foreach ($directories as $dir) {
    echo "\nSyncing $dir files...\n";
    list($synced, $errors) = syncDirectory(
        storage_path('uploads/' . $dir),
        public_path('storage/' . $dir)
    );
    $totalSynced += $synced;
    $totalErrors += $errors;
}

echo "\n=== Synchronization Complete ===\n";
echo "Total files synced: $totalSynced\n";
echo "Total errors: $totalErrors\n";

// Test image URLs after sync
echo "\n=== Testing Image URLs ===\n";
$testUser = \App\Models\User::where('avatar', '!=', null)->first();
if ($testUser) {
    echo "Test user: {$testUser->name}\n";
    echo "Avatar: {$testUser->avatar}\n";
    
    $avatarUrl = \App\Models\Utility::get_file('uploads/avatar') . '/' . $testUser->avatar;
    echo "Generated URL: $avatarUrl\n";
    
    $publicFile = public_path('storage/avatar/' . $testUser->avatar);
    echo "Public file exists: " . (file_exists($publicFile) ? 'YES' : 'NO') . "\n";
}

echo "\nDone.\n";
